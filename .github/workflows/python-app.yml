# Name of your workflow
name: Python application

# Controls when the workflow will run
on:
  push:
    branches: [ "main" ]
  pull_request:
    branches: [ "main" ]

# Sets the permissions for the GitHub token
permissions:
  contents: read

# Defines the jobs that will run as part of the workflow
jobs:
  build:
    # Specifies the runner for the job
    runs-on: ubuntu-latest

    # A sequence of tasks that will be executed as part of the job
    steps:
    # Checks out your repository's code
    - uses: actions/checkout@v4

    # Sets up the specified version of Python
    - name: Set up Python 3.13
      uses: actions/setup-python@v5
      with:
        python-version: "3.13"

    # ⬇️ ADD THIS NEW STEP ⬇️
    # Installs the Graphviz system library required by pygraphviz
    - name: Install Graphviz
      run: |
        sudo apt-get update
        sudo apt-get install -y graphviz libgraphviz-dev

    # Installs uv and your project's dependencies
    - name: Install uv and dependencies
      run: |
        python -m pip install --upgrade pip
        pip install uv
        # Install testing tools (like flake8) that might not be in your project dependencies
        uv pip install --system flake8
        # Install the project itself and all its dependencies from pyproject.toml
        uv pip install --system -e .
        
    # Checks your code formatting with black
    - name: Check formatting with black
      run: |
        black --check .

    # Lints your code with flake8
    - name: Lint with flake8
      run: |
        flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
        flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics

    # Runs your tests with pytest
    - name: Test with pytest
      run: |
        pytest