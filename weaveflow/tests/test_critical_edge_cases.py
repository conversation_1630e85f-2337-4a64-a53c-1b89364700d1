"""
Critical edge case tests for WeaveFlow framework.

This module contains tests for critical error conditions, edge cases,
and potential failure modes that could cause issues in production.
"""

import os
import shutil
import tempfile
import threading
import time
from dataclasses import dataclass
from pathlib import Path

import pandas as pd
import pytest

from weaveflow import weave, spool_asset, refine, Loom
from weaveflow.options import set_weaveflow_option
from weaveflow._decorators._spool import SPoolRegistry, _load_config_data


def test_concurrent_file_access():
    """Test behavior when multiple processes try to access same config file."""
    results = []
    errors = []
    
    def load_config():
        try:
            @spool_asset(file="stock.toml")
            @dataclass
            class ConcurrentData:
                price: float
            
            data = ConcurrentData()
            results.append(data.price)
        except Exception as e:
            errors.append(e)
    
    # Create multiple threads accessing same config
    threads = [threading.Thread(target=load_config) for _ in range(5)]
    
    for thread in threads:
        thread.start()
    
    for thread in threads:
        thread.join()
    
    # All threads should succeed
    assert len(errors) == 0
    assert len(results) == 5
    assert all(price == 233.33 for price in results)


def test_memory_usage_with_large_config():
    """Test memory behavior with very large config files."""
    # Create a large TOML file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.toml', delete=False) as f:
        # Write a large config with many keys
        for i in range(1000):
            f.write(f'key_{i} = {i}\n')
        temp_path = f.name
    
    try:
        @spool_asset(file=os.path.basename(temp_path))
        @dataclass
        class LargeConfigData:
            key_0: int
            key_999: int
        
        set_weaveflow_option("asset_path", Path(temp_path).parent)
        
        data = LargeConfigData()
        assert data.key_0 == 0
        assert data.key_999 == 999
        
    finally:
        os.unlink(temp_path)


def test_deeply_nested_loom_execution():
    """Test Loom with many sequential weave tasks."""
    @weave(outputs="step1")
    def task1(col1: pd.Series) -> pd.Series:
        return col1 * 2
    
    @weave(outputs="step2") 
    def task2(step1: pd.Series) -> pd.Series:
        return step1 + 1
    
    @weave(outputs="step3")
    def task3(step2: pd.Series) -> pd.Series:
        return step2 * 3
    
    @weave(outputs="step4")
    def task4(step3: pd.Series) -> pd.Series:
        return step4 / 2
    
    @weave(outputs="final")
    def task5(step4: pd.Series) -> pd.Series:
        return step4 - 10
    
    base_df = pd.DataFrame({"col1": [1, 2, 3, 4, 5]})
    
    loom = Loom(
        database=base_df,
        weave_tasks=[task1, task2, task3, task4, task5]
    )
    loom.run()
    
    # Verify the chain of transformations
    assert "final" in loom.database.columns
    assert len(loom.database) == 5


def test_config_file_modification_during_load():
    """Test behavior when config file is modified during loading."""
    with tempfile.NamedTemporaryFile(mode='w', suffix='.toml', delete=False) as f:
        f.write('value = 100\n')
        temp_path = f.name
    
    def modify_file():
        time.sleep(0.1)  # Small delay
        with open(temp_path, 'w') as f:
            f.write('value = 200\n')
    
    try:
        # Start file modification in background
        modifier_thread = threading.Thread(target=modify_file)
        modifier_thread.start()
        
        @spool_asset(file=os.path.basename(temp_path))
        @dataclass
        class ModifiedData:
            value: int
        
        set_weaveflow_option("asset_path", Path(temp_path).parent)
        
        data = ModifiedData()
        modifier_thread.join()
        
        # Should get one of the values (race condition, but shouldn't crash)
        assert data.value in [100, 200]
        
    finally:
        os.unlink(temp_path)


def test_circular_weave_dependencies():
    """Test detection of circular dependencies in weave tasks."""
    @weave(outputs="a")
    def task_a(b: pd.Series) -> pd.Series:  # Depends on b
        return b * 2
    
    @weave(outputs="b") 
    def task_b(a: pd.Series) -> pd.Series:  # Depends on a
        return a + 1
    
    base_df = pd.DataFrame({"col1": [1, 2, 3]})
    
    loom = Loom(
        database=base_df,
        weave_tasks=[task_a, task_b]
    )
    
    # Should fail due to circular dependency
    with pytest.raises(KeyError):  # Missing required columns
        loom.run()


def test_unicode_in_config_files():
    """Test handling of Unicode characters in config files."""
    with tempfile.NamedTemporaryFile(mode='w', suffix='.toml', delete=False, encoding='utf-8') as f:
        f.write('city = "München"\n')
        f.write('description = "Café in Zürich"\n')
        f.write('emoji = "🚀"\n')
        temp_path = f.name
    
    try:
        @spool_asset(file=os.path.basename(temp_path))
        @dataclass
        class UnicodeData:
            city: str
            description: str
            emoji: str
        
        set_weaveflow_option("asset_path", Path(temp_path).parent)
        
        data = UnicodeData()
        assert data.city == "München"
        assert data.description == "Café in Zürich"
        assert data.emoji == "🚀"
        
    finally:
        os.unlink(temp_path)


def test_extremely_long_file_paths():
    """Test behavior with very long file paths."""
    # Create nested directory structure
    base_temp = tempfile.mkdtemp()
    long_path = base_temp
    
    # Create a deeply nested path
    for i in range(10):
        long_path = os.path.join(long_path, f"very_long_directory_name_{i}")
        os.makedirs(long_path, exist_ok=True)
    
    config_file = os.path.join(long_path, "config.toml")
    
    try:
        with open(config_file, 'w') as f:
            f.write('value = 42\n')
        
        @spool_asset(file="config.toml")
        @dataclass
        class LongPathData:
            value: int
        
        set_weaveflow_option("asset_path", Path(long_path))
        
        data = LongPathData()
        assert data.value == 42
        
    finally:
        shutil.rmtree(base_temp)


def test_config_with_special_characters():
    """Test config files with special characters in keys and values."""
    with tempfile.NamedTemporaryFile(mode='w', suffix='.toml', delete=False) as f:
        f.write('[section]\n')
        f.write('"key-with-dashes" = "value with spaces"\n')
        f.write('"key.with.dots" = "value@with#special$chars%"\n')
        temp_path = f.name
    
    try:
        data = {}
        # Load manually to test special character handling
        data = _load_config_data(path=Path(temp_path).parent, specific_file=Path(temp_path).name)
        
        assert data["section"]["key-with-dashes"] == "value with spaces"
        assert data["section"]["key.with.dots"] == "value@with#special$chars%"
        
    finally:
        os.unlink(temp_path)


def test_weave_task_exception_propagation():
    """Test that exceptions in weave tasks are properly propagated."""
    @weave(outputs="error_result")
    def failing_task(col1: pd.Series) -> pd.Series:
        raise RuntimeError("Intentional task failure")
    
    base_df = pd.DataFrame({"col1": [1, 2, 3]})
    loom = Loom(database=base_df, weave_tasks=[failing_task])
    
    with pytest.raises(RuntimeError, match="Intentional task failure"):
        loom.run()


def test_refine_task_with_invalid_return_type():
    """Test refine task that returns invalid type for DataFrame operations."""
    @refine
    def bad_refine(df: pd.DataFrame) -> str:  # Should return DataFrame
        return "not a dataframe"
    
    test_df = pd.DataFrame({"a": [1, 2, 3]})
    
    # The refine decorator itself should work, but using result in Loom might fail
    result = bad_refine(test_df)
    assert result == "not a dataframe"
