from dataclasses import dataclass
from pathlib import Path
from pandas import DataFrame, read_csv
from pandas.testing import assert_frame_equal
from weaveflow._decorators import spool_asset
from weaveflow._decorators._spool import SPoolRegistry
from weaveflow.options import set_weaveflow_option


set_weaveflow_option("asset_path", Path(__file__).parent / "data")


@spool_asset
def stock_data(
    price: float,
    pe_ratio: float,
    ps_ratio: float,
    peg_ratio: float,
    pb_ratio: float,
) -> SPoolRegistry:
    """Returns numeric constants from the registry."""


@spool_asset(file="stock.toml")
@dataclass
class StockData:
    """Data class for collection all variables from registry config files."""

    price: float
    pe_ratio: float
    ps_ratio: float
    peg_ratio: float
    pb_ratio: float


@spool_asset
@dataclass
class DummyDataAsset:
    """Dummy asset class for testing."""

    dummy1: int
    dummy2: int
    dummy3: int
    id1: float
    id2: float
    id3: float


@spool_asset(custom_engine={"csv": read_csv})
@dataclass
class CityCosts:
    costs: DataFrame


def test_spool_toml_function():
    """Test spool decorator with TOML files."""
    # Use a different variable name for the result
    data = stock_data()

    assert hasattr(stock_data, "_spool")
    assert isinstance(data, SPoolRegistry)
    assert data.price == 233.33
    assert data.pe_ratio == 35.41
    assert data.ps_ratio == 8.62
    assert data.peg_ratio == 116.31
    assert data.pb_ratio == 52.6
    assert data.__dict__ == {
        "price": 233.33,
        "pe_ratio": 35.41,
        "ps_ratio": 8.62,
        "peg_ratio": 116.31,
        "pb_ratio": 52.6,
    }


def test_spool_toml_class():
    """Test spool decorator with TOML files."""
    # Use a different variable name for the result
    data = StockData()

    assert hasattr(StockData, "_spool")
    assert isinstance(data, StockData)
    assert data.price == 233.33
    assert data.pe_ratio == 35.41
    assert data.ps_ratio == 8.62
    assert data.peg_ratio == 116.31
    assert data.pb_ratio == 52.6
    assert data.__dict__ == {
        "price": 233.33,
        "pe_ratio": 35.41,
        "ps_ratio": 8.62,
        "peg_ratio": 116.31,
        "pb_ratio": 52.6,
    }


def test_spool_asset():
    """Test spool_asset decorator."""
    data = DummyDataAsset()

    assert hasattr(DummyDataAsset, "_spool")
    assert isinstance(data, DummyDataAsset)
    assert data.dummy1 == 10
    assert data.dummy2 == 20
    assert data.dummy3 == 30
    assert data.id1 == 1.4
    assert data.id2 == 2.3
    assert data.id3 == 1.2


def test_custom_engine_in_spool():
    """Test custom engine in spool decorator."""
    city_costs = CityCosts()
    assert isinstance(city_costs.costs, DataFrame)
    expected_data = read_csv(Path(__file__).parent / "data" / "costs.csv")
    assert_frame_equal(city_costs.costs, expected_data)


def test_spool_function_missing_required_args():
    """Test spool decorator on function when required args are missing from config."""
    @spool_asset
    def incomplete_stock_data(
        price: float,
        missing_arg: float,  # This arg doesn't exist in stock.toml
    ) -> SPoolRegistry:
        """Function with missing required argument."""

    with pytest.raises(ValueError, match="Required arg 'missing_arg' not found in config"):
        incomplete_stock_data()


def test_spool_with_runtime_kwargs_override():
    """Test that runtime kwargs override config values in spool decorator."""
    @spool_asset(file="stock.toml")
    @dataclass
    class OverrideStockData:
        price: float
        pe_ratio: float

    # Override price at runtime
    data = OverrideStockData(price=999.99)
    assert data.price == 999.99  # Runtime value should win
    assert data.pe_ratio == 35.41  # Config value should be used


def test_spool_with_invalid_asset_path():
    """Test spool_asset behavior when asset_path is invalid."""
    from weaveflow.options import set_weaveflow_option

    # Save original path
    original_path = Path(__file__).parent / "data"

    try:
        # Set invalid path
        set_weaveflow_option("asset_path", Path("/nonexistent/path"))

        with pytest.raises(FileNotFoundError):
            @spool_asset(file="stock.toml")
            @dataclass
            class InvalidPathData:
                price: float

            InvalidPathData()
    finally:
        # Restore original path
        set_weaveflow_option("asset_path", original_path)


def test_spool_registry_from_file():
    """Test SPoolRegistry.from_file class method."""
    registry = SPoolRegistry.from_file(Path(__file__).parent / "data" / "stock.toml")

    assert isinstance(registry, SPoolRegistry)
    assert registry.price == 233.33
    assert registry.pe_ratio == 35.41
    assert hasattr(registry, '__dict__')


def test_spool_class_without_dataclass_or_init():
    """Test spool decorator on class without @dataclass or explicit __init__."""
    # This should fail because there's no way to set attributes
    @spool_asset(file="stock.toml")
    class PlainClass:
        price: float
        pe_ratio: float

    # Should create instance but attributes won't be set due to missing __init__
    data = PlainClass()
    with pytest.raises(AttributeError):
        _ = data.price  # Should fail because no attributes were set
