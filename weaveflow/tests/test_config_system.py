"""
Tests for the WeaveFlow configuration system.

This module tests the global configuration options and their interactions
with the spool decorators and asset loading system.
"""

import pytest
from pathlib import Path
from weaveflow.options import set_weaveflow_option
from weaveflow._utils._config import _get_option, _settings


def test_set_weaveflow_option_single_value():
    """Test setting a single configuration option."""
    test_path = Path("/test/path")
    set_weaveflow_option("asset_path", test_path)
    
    assert _get_option("asset_path") == test_path


def test_set_weaveflow_option_multiple_values():
    """Test setting multiple configuration options at once."""
    test_path = Path("/test/path")
    test_exclude = "exclude_pattern"
    
    set_weaveflow_option(
        ["asset_path", "exclude_spool"], 
        [test_path, test_exclude]
    )
    
    assert _get_option("asset_path") == test_path
    assert _get_option("exclude_spool") == test_exclude


def test_set_weaveflow_option_invalid_key():
    """Test setting an invalid configuration option."""
    with pytest.raises(KeyError, match="Invalid option key: 'invalid_key'"):
        set_weaveflow_option("invalid_key", "some_value")


def test_set_weaveflow_option_invalid_key_type():
    """Test setting configuration with invalid key type."""
    with pytest.raises(TypeError, match="Key must be a string"):
        set_weaveflow_option([123], ["value"])


def test_set_weaveflow_option_invalid_value_type():
    """Test setting configuration with invalid value type."""
    with pytest.raises(TypeError, match="Value must be a string or a pathlib.Path"):
        set_weaveflow_option("asset_path", 123)


def test_set_weaveflow_option_non_iterable_keys():
    """Test setting configuration with non-iterable keys."""
    with pytest.raises(TypeError, match="Key must be a string or an iterable of strings"):
        set_weaveflow_option(123, "value")


def test_set_weaveflow_option_non_iterable_values():
    """Test setting configuration with non-iterable values."""
    with pytest.raises(TypeError, match="Value must be a string or an iterable of strings"):
        set_weaveflow_option("asset_path", 123)


def test_get_option_nonexistent_key():
    """Test getting a non-existent configuration option."""
    result = _get_option("nonexistent_key")
    assert result is None


def test_get_option_existing_key():
    """Test getting an existing configuration option."""
    test_path = Path("/test/path")
    set_weaveflow_option("asset_path", test_path)
    
    result = _get_option("asset_path")
    assert result == test_path


def test_config_isolation_between_tests():
    """Test that configuration changes don't leak between tests."""
    # Set a value
    set_weaveflow_option("asset_path", Path("/test1"))
    assert _get_option("asset_path") == Path("/test1")
    
    # Change it
    set_weaveflow_option("asset_path", Path("/test2"))
    assert _get_option("asset_path") == Path("/test2")
    
    # Verify it changed
    assert _get_option("asset_path") != Path("/test1")


def test_config_string_to_path_conversion():
    """Test that string paths are properly handled."""
    test_path_str = "/test/string/path"
    set_weaveflow_option("asset_path", test_path_str)
    
    # Should accept string but store as provided
    result = _get_option("asset_path")
    assert result == test_path_str
    assert isinstance(result, str)


def test_config_path_object_handling():
    """Test that Path objects are properly handled."""
    test_path = Path("/test/path/object")
    set_weaveflow_option("asset_path", test_path)
    
    result = _get_option("asset_path")
    assert result == test_path
    assert isinstance(result, Path)


def test_config_multiple_options_mismatch_length():
    """Test setting multiple options with mismatched lengths."""
    # This should work by zipping - extra values ignored
    set_weaveflow_option(
        ["asset_path", "exclude_spool"], 
        [Path("/test")]  # Only one value for two keys
    )
    
    # Only first key should be set
    assert _get_option("asset_path") == Path("/test")
    # Second key should remain unchanged from previous state


def test_config_empty_iterables():
    """Test setting configuration with empty iterables."""
    # Should handle empty iterables gracefully
    set_weaveflow_option([], [])
    
    # No changes should occur - this should not raise an error


def test_config_settings_direct_access():
    """Test that _settings dictionary is properly protected."""
    # Should not be able to directly modify _settings
    original_keys = set(_settings.keys())
    
    # Verify expected keys exist
    expected_keys = {"asset_path", "exclude_spool", "include_spool"}
    assert original_keys == expected_keys


def test_config_option_persistence():
    """Test that configuration options persist across multiple operations."""
    test_path = Path("/persistent/path")
    set_weaveflow_option("asset_path", test_path)
    
    # Perform multiple get operations
    for _ in range(5):
        result = _get_option("asset_path")
        assert result == test_path
    
    # Set another option
    set_weaveflow_option("exclude_spool", "pattern")
    
    # Original option should still be there
    assert _get_option("asset_path") == test_path
    assert _get_option("exclude_spool") == "pattern"


def test_config_none_values():
    """Test handling of None values in configuration."""
    # Setting to None should work (reset to default)
    set_weaveflow_option("asset_path", None)
    
    # Should be able to get None back
    result = _get_option("asset_path")
    assert result is None


def test_config_overwrite_existing():
    """Test overwriting existing configuration values."""
    # Set initial value
    set_weaveflow_option("asset_path", Path("/initial"))
    assert _get_option("asset_path") == Path("/initial")
    
    # Overwrite with new value
    set_weaveflow_option("asset_path", Path("/overwritten"))
    assert _get_option("asset_path") == Path("/overwritten")
    
    # Should not have the old value
    assert _get_option("asset_path") != Path("/initial")


def test_config_all_valid_options():
    """Test that all expected configuration options are available."""
    valid_options = ["asset_path", "exclude_spool", "include_spool"]
    
    for option in valid_options:
        # Should not raise an error
        set_weaveflow_option(option, "test_value")
        result = _get_option(option)
        assert result == "test_value"


def test_config_case_sensitivity():
    """Test that configuration keys are case-sensitive."""
    set_weaveflow_option("asset_path", Path("/test"))
    
    # Different case should be invalid
    with pytest.raises(KeyError):
        set_weaveflow_option("Asset_Path", Path("/test"))
    
    with pytest.raises(KeyError):
        set_weaveflow_option("ASSET_PATH", Path("/test"))
