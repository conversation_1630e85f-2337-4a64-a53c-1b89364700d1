from dataclasses import dataclass

import pandas as pd
import pytest

from weaveflow import Loom, weave, rethread
from weaveflow._decorators._meta import WeaveMeta
from weaveflow._decorators._weave import _is_weave


@weave(outputs="sum")
def add_columns(col1: pd.Series, col2: pd.Series):
    return col1 + col2


@weave(outputs="diff")
def subtract_columns(col1: pd.Series, col2: pd.Series):
    return col2 - col1


@weave(outputs=["mul", "div"])
def calculate_stats(sum: pd.Series, diff: pd.Series, margin: int = 0):
    return sum * diff + margin, (sum / diff).astype(int) - margin


@weave(outputs="scaled_mul")
def scale_sum(mul: pd.Series, scaler: float | int = 2):
    return mul * scaler


@weave(outputs="scaled_col1")
def margin_scaled(col1: pd.Series, scaler: int = 1, margin: int = 0):
    return col1 * scaler + margin


def test_weave_decorator_attributes():

    assert _is_weave(add_columns)
    assert _is_weave(scale_sum)

    add_columns_weave_meta = getattr(add_columns, "_weave_meta")
    scale_sum_weave_meta = getattr(scale_sum, "_weave_meta")

    assert isinstance(add_columns_weave_meta, WeaveMeta)
    assert isinstance(scale_sum_weave_meta, WeaveMeta)

    add_columns_weave_meta._rargs == ["col1", "col2"]
    add_columns_weave_meta._oargs == []
    add_columns_weave_meta._outputs == ["sum"]

    scale_sum_weave_meta._rargs == ["mul"]
    scale_sum_weave_meta._oargs == ["scaler"]
    scale_sum_weave_meta._outputs == ["scaled_mul"]


def test_error_on_non_weave(base_dataframe_input):

    # Raise KeyError if some required arguments are not found in database for `Loom`
    with pytest.raises(KeyError):
        Loom(database=base_dataframe_input, weave_tasks=[calculate_stats]).run()

    def invalid_inputs_weave(col1: pd.Series):
        return col1 + 1

    def invalid_optional_arg_weave(constant: int = 42):
        return constant

    def invalid_optional_arg_weave_ninputs(constant: int):
        return constant

    # Raise ValueError if ninputs is not an integer or is negative
    with pytest.raises(ValueError):
        weave(outputs="col1_plus_1", nrargs=2.0)(
            invalid_inputs_weave
        )  # ninputs is not an integer
        weave(outputs="col1_plus_1", nrargs=-1)(
            invalid_inputs_weave
        )  # ninputs is negative
        weave(outputs="constant", nrargs=1)(
            invalid_optional_arg_weave
        )  # ninputs is specified but function has optional arguments
        weave(outputs="constant", nrargs=2)(
            invalid_optional_arg_weave_ninputs
        )  # ninputs greater than number of required arguments


@pytest.mark.parametrize("weave_func", [add_columns, subtract_columns])
def test_weave_runs(base_dataframe, base_dataframe_input, weave_func):
    """Tests weave 'add_columns' and 'subtract_columns' one-by-one."""
    # 'weave_func' is now the actual function object
    loom = Loom(database=base_dataframe_input, weave_tasks=[weave_func])
    loom.run()
    meta = getattr(weave_func, "_weave_meta")
    expected_df = base_dataframe[meta._rargs + meta._outputs]
    pd.testing.assert_frame_equal(loom.database, expected_df)


def test_weave_with_multiple_outputs(base_dataframe, base_dataframe_input):
    """Tests whole weave for defined weave tasks."""
    # Test whole weave
    loom = Loom(
        database=base_dataframe_input,
        weave_tasks=[add_columns, subtract_columns, calculate_stats, scale_sum],
    )
    loom.run()
    pd.testing.assert_frame_equal(loom.database, base_dataframe)


def test_weave_with_optionals(base_dataframe, base_dataframe_input):
    """Tests whole weave with changed optional arguments for defined weave tasks."""

    # Define attributes for optional arguments
    margin = 2
    scaler = 1

    # Define expected output based on optional arguments
    base_dataframe_modified = base_dataframe.copy()
    base_dataframe_modified["mul"] += margin
    base_dataframe_modified["div"] -= margin
    base_dataframe_modified["scaled_mul"] = base_dataframe_modified["mul"] * scaler

    # --- Test task-specific optionals ---

    # Define optionals arg for 'Loom' via optionals dict
    optionals = {calculate_stats: {"margin": margin}, "scale_sum": {"scaler": scaler}}
    loom = Loom(
        database=base_dataframe_input,
        weave_tasks=[add_columns, subtract_columns, calculate_stats, scale_sum],
        optionals=optionals,
    )
    loom.run()
    pd.testing.assert_frame_equal(loom.database, base_dataframe_modified)

    # --- Test global optionals ---

    # Define optionals arg for 'Loom' via kwargs
    loom = Loom(
        database=base_dataframe_input,
        weave_tasks=[add_columns, subtract_columns, calculate_stats, scale_sum],
        margin=margin,
        scaler=scaler,
    )
    loom.run()
    pd.testing.assert_frame_equal(loom.database, base_dataframe_modified)

    # --- Test task-specific and global optionals ---

    # Create expected column output
    base_dataframe_modified["scaled_col1"] = base_dataframe_modified["col1"] * 2 + 1

    # Define optionals arg for 'Loom' via kwargs and optionals dict
    loom = Loom(
        database=base_dataframe_input,
        weave_tasks=[
            add_columns,
            subtract_columns,
            calculate_stats,
            scale_sum,
            margin_scaled,
        ],
        optionals={
            margin_scaled: {"scaler": 2, "margin": 1}
        },  # Make task-specific optionals
        margin=margin,
        scaler=scaler,
    )
    loom.run()

    pd.testing.assert_frame_equal(loom.database, base_dataframe_modified)


def test_rethread(base_dataframe_input: pd.DataFrame):

    meta = {"col1": "diff", "col2": "sum"}
    calculate_stats_t = rethread(calculate_stats, meta=meta)
    loom = Loom(database=base_dataframe_input, weave_tasks=[calculate_stats_t])
    loom.run()

    assert (
        list(loom.database.columns)
        == list(meta.keys()) + calculate_stats_t._weave_meta._outputs
    )

    expected_df = base_dataframe_input.copy()
    expected_df["mul"] = expected_df["col1"] * expected_df["col2"]
    expected_df["div"] = (expected_df["col2"] / expected_df["col1"]).astype(int)

    pd.testing.assert_frame_equal(loom.database, expected_df)


def test_loom_with_invalid_weave_task():
    """Test Loom behavior when non-weave task is passed to weave_tasks."""
    def not_a_weave_task(x):
        return x * 2

    base_df = pd.DataFrame({"col1": [1, 2, 3], "col2": [4, 5, 6]})

    with pytest.raises(TypeError, match=f"Argument 'weave_tasks' contains a non-weave and non-refine task: {not_a_weave_task!r}"):
        Loom(
            database=base_df,
            weave_tasks=[add_columns, not_a_weave_task],  # Mix of valid and invalid
        )


def test_loom_with_missing_columns():
    """Test Loom behavior when required columns are missing from database."""
    # Create DataFrame missing required columns
    incomplete_df = pd.DataFrame({"col1": [1, 2, 3]})  # Missing col2

    loom = Loom(
        database=incomplete_df,
        weave_tasks=[add_columns],  # Requires both col1 and col2
    )

    with pytest.raises(KeyError):  # Should fail when trying to access missing col2
        loom.run()


def test_loom_with_empty_dataframe():
    """Test Loom behavior with empty DataFrame."""
    empty_df = pd.DataFrame(columns=["col1", "col2"])

    loom = Loom(
        database=empty_df,
        weave_tasks=[add_columns],
    )
    loom.run()

    # Should work but result in empty DataFrame with new columns
    assert len(loom.database) == 0
    assert "sum" in loom.database.columns


def test_weave_function_returning_none():
    """Test weave task that returns None."""
    @weave(outputs="null_result")
    def return_none(col1: pd.Series) -> None:
        return None

    base_df = pd.DataFrame({"col1": [1, 2, 3]})
    loom = Loom(database=base_df, weave_tasks=[return_none])
    loom.run()

    # None-returning tasks should be skipped
    assert "null_result" not in loom.database.columns
    assert len(loom.weave_collector["default"]) == 0  # Task should be removed


def test_weave_with_invalid_output_count():
    """Test weave task that returns wrong number of outputs."""
    @weave(outputs=["out1", "out2"])  # Expects 2 outputs
    def wrong_output_count(col1: pd.Series):
        return col1 * 2  # Returns only 1 output

    base_df = pd.DataFrame({"col1": [1, 2, 3]})
    loom = Loom(database=base_df, weave_tasks=[wrong_output_count])

    with pytest.raises((ValueError, TypeError)):  # Should fail during unpacking
        loom.run()


def test_rethread_on_non_weave_function():
    """Test rethread decorator on function that's not a weave task."""
    def regular_function(x):
        return x * 2

    with pytest.raises(TypeError, match="Function must be a weave task"):
        rethread(regular_function, meta={"x": "y"})


def test_weave_with_conflicting_nrargs_and_params_from():
    """Test weave decorator with both nrargs and params_from specified."""
    @dataclass
    class DummyParams:
        value: int = 10

    with pytest.raises(ValueError, match="Cannot use 'nrargs' and 'params_from' at the same time"):
        @weave(outputs="result", nrargs=2, params_from=DummyParams)
        def conflicting_weave(col1: pd.Series, col2: pd.Series, value: int):
            return col1 + col2 + value


def test_weave_with_invalid_outputs_type():
    """Test weave decorator with invalid outputs parameter type."""
    with pytest.raises(ValueError, match="must be a string or a list of strings"):
        @weave(outputs=123)  # Invalid type
        def invalid_outputs_weave(col1: pd.Series):
            return col1 * 2


def test_weave_function_with_optional_args_and_nrargs():
    """Test weave decorator with optional args when nrargs is specified."""
    with pytest.raises(ValueError, match="Function has optional arguments, but 'nrargs' is specified"):
        @weave(outputs="result", nrargs=2)
        def weave_with_optionals(col1: pd.Series, col2: pd.Series, optional: int = 5):
            return col1 + col2 + optional


def test_weave_function_insufficient_args_for_nrargs():
    """Test weave decorator when function has fewer args than nrargs."""
    with pytest.raises(ValueError, match="requires at least 3 inputs, but only 2 were found"):
        @weave(outputs="result", nrargs=3)
        def insufficient_args(col1: pd.Series, col2: pd.Series):
            return col1 + col2


def test_loom_database_modification_during_execution():
    """Test Loom behavior when database is modified during execution."""
    @weave(outputs="doubled")
    def double_values(col1: pd.Series) -> pd.Series:
        return col1 * 2

    @weave(outputs="tripled")
    def triple_values(col1: pd.Series) -> pd.Series:
        # Simulate external modification of the database
        return col1 * 3

    base_df = pd.DataFrame({"col1": [1, 2, 3, 4, 5]})
    loom = Loom(database=base_df, weave_tasks=[double_values, triple_values])

    # Should work normally - each task operates on original columns
    loom.run()

    assert "doubled" in loom.database.columns
    assert "tripled" in loom.database.columns
    assert list(loom.database["doubled"]) == [2, 4, 6, 8, 10]
    assert list(loom.database["tripled"]) == [3, 6, 9, 12, 15]
