from pathlib import Path
from pandas import DataFrame, read_csv
from pandas.testing import assert_frame_equal
import pytest
from weaveflow._decorators._spool import (
    _handle_files_from_iterable,
    _load_config_data,
)
from weaveflow.tests.data.dummy.dummy import dummy_function


def test_handle_elements_from_iterable():
    """Test the handle_elements_from_iterable function."""
    # Test with a single string pattern
    assert _handle_files_from_iterable(["a", "b", "c"], None) == ["a", "b", "c"]
    assert _handle_files_from_iterable(["a", "b", "c"], "a") == ["a"]
    assert _handle_files_from_iterable(("a", "b", "c"), ("a", "b")) == ["a", "b"]
    assert _handle_files_from_iterable(["a", "b", "c"], "d") == []
    assert _handle_files_from_iterable(["a", "b", "c"], ["a", "b"], include=False) == [
        "c"
    ]


def test_handle_elements_from_iterable_with_path():
    """Test the handle_elements_from_iterable function with Path objects."""
    from pathlib import Path

    files = [
        Path("/home/<USER>/file_registry.json"),
        Path("/home/<USER>/file_registry.toml"),
        Path("/home/<USER>/file_registry2.yaml"),
        Path("/home/<USER>/file_spool.yml"),
        Path("/home/<USER>/spool/somefile.txt"),
    ]
    assert _handle_files_from_iterable(files, "spool") == [
        Path("/home/<USER>/file_spool.yml")
    ]
    assert _handle_files_from_iterable(files, "registry") == [
        Path("/home/<USER>/file_registry.json"),
        Path("/home/<USER>/file_registry.toml"),
        Path("/home/<USER>/file_registry2.yaml"),
    ]


def test_load_config_data_specific_file_not_found(test_data_path: Path):
    """Test error when specific file doesn't exist."""
    with pytest.raises(FileNotFoundError, match="Specified config file not found"):
        _load_config_data(path=test_data_path, specific_file="nonexistent.json")


def test_load_config_data_path_not_found(test_data_path):
    """Test error when path doesn't exist."""
    with pytest.raises(
        FileNotFoundError,
        match=f"Specified path not found: {test_data_path / "no_dir"}",
    ):
        _load_config_data(path=test_data_path / "no_dir")


def test_load_config_data_no_config_files(test_data_path):
    """Test error when no config files found in directory."""
    with pytest.raises(
        FileNotFoundError,
        match=f"No config files found in {test_data_path / "empty_dir"}.",
    ):
        _load_config_data(path=test_data_path / "empty_dir")


def test_load_config_data_both_include_exclude_error():
    """Test error when both include and exclude are specified."""
    with pytest.raises(ValueError, match="Cannot specify both 'exclude' and 'include'"):
        _load_config_data(path="/tmp", include=["a"], exclude=["b"])


def test_load_config_data_specific_file_with_include_exclude_error():
    """Test error when specific_file is used with include/exclude."""
    with pytest.raises(
        ValueError, match="Cannot specify both 'specific_file' and 'exclude/include'"
    ):
        _load_config_data(path="/tmp", specific_file="config.json", include=["a"])


def test_load_config_data_empty_configs(test_data_path):
    """Test error when only empty config files found in directory."""
    with pytest.raises(
        ValueError, match="Config files found, but no data found in config files."
    ):
        _load_config_data(
            path=test_data_path, exclude=["dummy_spool"]
        )  # dummy_spool.yaml is not empty, exclude to raise error


def test_custom_config_loader_type_error(test_data_path):
    """Test custom config loader."""
    with pytest.raises(
        TypeError,
        match="Custom engine must be a dict mapping file extensions to read function.",
    ):
        _load_config_data(path=test_data_path, custom_engine="nonexistent")


def test_load_config_data_no_obj_or_path_error():
    """Test error when neither obj nor path is specified."""
    with pytest.raises(ValueError, match="Either 'obj' or 'path' must be specified"):
        _load_config_data()


def test_load_config_data_with_obj():
    """Test loading config data with an object."""
    data = _load_config_data(obj=dummy_function)
    assert data == {"a": 1, "b": 2, "c": 3, "d": 4}


def test_load_config_data_with_path(test_data_path):
    """Test loading config data with a path."""
    data = _load_config_data(path=test_data_path)
    assert data == {"a": 1, "b": 2, "c": 3, "d": 4}


def test_load_config_data_with_specific_file(test_data_path):
    """Test loading config data with a specific file."""
    data = _load_config_data(path=test_data_path, specific_file="dummy_spool.yaml")
    assert data == {"a": 1, "b": 2}
    data = _load_config_data(path=test_data_path, include="dummy_spool.yaml")
    assert data == {"a": 1, "b": 2}
    data = _load_config_data(path=test_data_path, exclude="dummy_spool.yaml")
    assert data == {"c": 3, "d": 4}


def test_load_csv_custom_engine(test_data_path):
    """Tests _load_config_data with custom engine."""
    data_path = test_data_path.parent
    data = _load_config_data(
        path=data_path,
        custom_engine={"csv": read_csv},  # Extend default engine with csv reader
        include="costs",  # Only include filenames containing "costs", costs.csv and costs.toml
    )
    data2 = _load_config_data(
        path=data_path, custom_engine={"csv": read_csv}, specific_file="costs.csv"
    )

    assert_frame_equal(data["costs"], data2["costs"])
    assert isinstance(data, dict) and isinstance(data2, dict)
    # Content from csv and toml, while csv is wrapped in dict
    assert (len(data) == 4) and (len(data2) == 1)
    assert isinstance(data["costs"], DataFrame)
    expected_data = read_csv(data_path / "costs.csv")
    assert_frame_equal(data["costs"], expected_data)
    assert data["city_dict"] == {
        "Cologne": 1000,
        "Berlin": 1250,
        "Munich": 1500,
        "Hamburg": 1210,
        "Frankfurt": 1380,
    }
    assert data["children_dict"] == {"0": 0, "1": 400, "2": 700, "3": 950}
    assert data["subscription_int"] == 45


def test_load_config_data_with_malformed_yaml(test_data_path):
    """Test _load_config_data behavior with malformed YAML file."""
    import tempfile
    import os

    with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
        f.write("invalid: yaml: content: [unclosed")  # Malformed YAML
        temp_path = f.name

    try:
        with pytest.raises(ValueError, match="Error decoding YAML file"):
            _load_config_data(path=Path(temp_path).parent, specific_file=Path(temp_path).name)
    finally:
        os.unlink(temp_path)


def test_load_config_data_with_malformed_json(test_data_path):
    """Test _load_config_data behavior with malformed JSON file."""
    import tempfile
    import os

    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        f.write('{"invalid": json, "missing": quote}')  # Malformed JSON
        temp_path = f.name

    try:
        with pytest.raises(ValueError, match="Error decoding JSON file"):
            _load_config_data(path=Path(temp_path).parent, specific_file=Path(temp_path).name)
    finally:
        os.unlink(temp_path)


def test_load_config_data_with_malformed_toml(test_data_path):
    """Test _load_config_data behavior with malformed TOML file."""
    import tempfile
    import os

    with tempfile.NamedTemporaryFile(mode='w', suffix='.toml', delete=False) as f:
        f.write('[invalid toml\nkey = "missing bracket"')  # Malformed TOML
        temp_path = f.name

    try:
        with pytest.raises(ValueError, match="Error decoding TOML file"):
            _load_config_data(path=Path(temp_path).parent, specific_file=Path(temp_path).name)
    finally:
        os.unlink(temp_path)


def test_custom_engine_with_duplicate_key_error(test_data_path):
    """Test custom engine behavior when duplicate keys are found."""
    import tempfile
    import os

    # Create two CSV files with same stem name
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f1:
        f1.write("col1,col2\n1,2\n")
        temp_path1 = f1.name

    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False,
                                     dir=Path(temp_path1).parent,
                                     prefix=Path(temp_path1).stem) as f2:
        f2.write("col1,col2\n3,4\n")
        temp_path2 = f2.name

    try:
        # This should raise error due to duplicate keys
        with pytest.raises(ValueError, match="Duplicate key.*found in config files"):
            _load_config_data(
                path=Path(temp_path1).parent,
                custom_engine={"csv": read_csv}
            )
    finally:
        os.unlink(temp_path1)
        os.unlink(temp_path2)


def test_custom_engine_extension_normalization():
    """Test that custom engine extensions are normalized correctly."""
    from weaveflow._utils._reader import _ConfigReader

    # Test extension normalization (with and without dots)
    reader = _ConfigReader("test.csv", custom_engine={"csv": read_csv, ".txt": read_csv})

    # Both should be normalized to lowercase with dots
    assert ".csv" in reader._engine.__self__._engines if hasattr(reader._engine, '__self__') else True


def test_handle_files_from_iterable_edge_cases():
    """Test edge cases for _handle_files_from_iterable function."""
    # Test with empty iterable
    assert _handle_files_from_iterable([], "anything") == []

    # Test with None matching pattern
    assert _handle_files_from_iterable(["a", "b"], None) == ["a", "b"]

    # Test with empty string matching
    assert _handle_files_from_iterable(["", "a", "b"], "") == [""]

    # Test exclude mode with empty result
    assert _handle_files_from_iterable(["a"], "a", include=False) == []


def test_load_config_data_with_permission_error(test_data_path):
    """Test _load_config_data behavior when file permissions are denied."""
    import tempfile
    import os
    import stat

    with tempfile.NamedTemporaryFile(mode='w', suffix='.toml', delete=False) as f:
        f.write('key = "value"')
        temp_path = f.name

    try:
        # Remove read permissions
        os.chmod(temp_path, stat.S_IWRITE)

        with pytest.raises(FileNotFoundError):  # or PermissionError depending on system
            _load_config_data(path=Path(temp_path).parent, specific_file=Path(temp_path).name)
    finally:
        # Restore permissions and cleanup
        os.chmod(temp_path, stat.S_IREAD | stat.S_IWRITE)
        os.unlink(temp_path)
